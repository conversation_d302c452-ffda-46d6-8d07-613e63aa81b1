#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GOOGL和TSLA金融数据下载测试脚本

该脚本用于测试GOOGL和TSLA金融数据下载功能，验证API调用和数据保存是否正常工作。

使用方法:
    python test_googl_tsla_download.py
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_api_imports():
    """测试API模块导入"""
    print("🔍 测试API模块导入...")
    
    try:
        from src.tools.api import (
            get_line_items, get_market_cap, get_prices, 
            get_insider_trades, get_company_news, get_financial_metrics
        )
        print("  ✅ API模块导入成功")
        return True
    except ImportError as e:
        print(f"  ❌ API模块导入失败: {e}")
        return False

def test_single_api_call():
    """测试单个API调用"""
    print("\n🔍 测试单个API调用...")
    
    try:
        from src.tools.api import get_prices
        
        # 测试获取GOOGL价格数据
        print("  🔄 测试获取GOOGL价格数据...")
        data = get_prices("GOOGL", start_date="2024-01-01", end_date="2024-01-05")
        
        if data:
            print(f"  ✅ 成功获取 {len(data)} 条价格数据")
            print(f"  📊 样本数据: {str(data[0])[:100]}...")
            return True
        else:
            print("  ⚠️ 未获取到数据")
            return False
            
    except Exception as e:
        print(f"  ❌ API调用失败: {e}")
        return False

def test_data_directory_creation():
    """测试数据目录创建"""
    print("\n🔍 测试数据目录创建...")
    
    base_dir = Path("financial_data_offline")
    test_dirs = ["GOOGL_prices", "TSLA_prices", "GOOGL_line_items", "TSLA_line_items"]
    
    try:
        base_dir.mkdir(exist_ok=True)
        print(f"  ✅ 基础目录创建成功: {base_dir}")
        
        for test_dir in test_dirs:
            dir_path = base_dir / test_dir
            dir_path.mkdir(exist_ok=True)
            print(f"  ✅ 子目录创建成功: {dir_path}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 目录创建失败: {e}")
        return False

def test_json_file_operations():
    """测试JSON文件操作"""
    print("\n🔍 测试JSON文件操作...")
    
    try:
        test_data = {
            'data': ['test_data_1', 'test_data_2'],
            'metadata': {
                'download_time': datetime.now().isoformat(),
                'api_source': 'FINANCIAL_DATASETS_API',
                'ticker': 'TEST',
                'data_type': 'test'
            }
        }
        
        test_file = Path("financial_data_offline/test_file.json")
        
        # 写入测试
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)
        print("  ✅ JSON文件写入成功")
        
        # 读取测试
        with open(test_file, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        if loaded_data['data'] == test_data['data']:
            print("  ✅ JSON文件读取成功")
        else:
            print("  ❌ JSON文件读取数据不匹配")
            return False
        
        # 清理测试文件
        test_file.unlink()
        print("  ✅ 测试文件清理完成")
        
        return True
        
    except Exception as e:
        print(f"  ❌ JSON文件操作失败: {e}")
        return False

def test_environment_variables():
    """测试环境变量"""
    print("\n🔍 测试环境变量...")
    
    required_vars = ['FINANCIAL_DATASETS_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            print(f"  ✅ {var}: 已设置")
    
    if missing_vars:
        print(f"  ⚠️ 缺少环境变量: {', '.join(missing_vars)}")
        print("  💡 请在.env文件中设置这些变量")
        return False
    
    return True

def check_existing_data():
    """检查现有数据"""
    print("\n🔍 检查现有金融数据...")
    
    base_dir = Path("financial_data_offline")
    if not base_dir.exists():
        print("  ⚠️ financial_data_offline目录不存在")
        return
    
    tickers = ['AAPL', 'MSFT', 'NVDA', 'GOOGL', 'TSLA']
    data_types = ['prices', 'line_items', 'market_cap', 'insider_trades', 'company_news', 'financial_metrics']
    
    for ticker in tickers:
        print(f"\n  📊 {ticker}:")
        ticker_has_data = False
        
        for data_type in data_types:
            dir_path = base_dir / f"{ticker}_{data_type}"
            if dir_path.exists():
                file_count = len(list(dir_path.glob("*.json")))
                if file_count > 0:
                    print(f"    ✅ {data_type}: {file_count} 文件")
                    ticker_has_data = True
                else:
                    print(f"    ⚠️ {data_type}: 目录存在但无文件")
            else:
                print(f"    ❌ {data_type}: 目录不存在")
        
        if not ticker_has_data:
            print(f"    💡 {ticker} 需要下载金融数据")

def main():
    """主测试函数"""
    print("🧪 GOOGL和TSLA金融数据下载测试")
    print("=" * 50)
    
    tests = [
        ("API模块导入", test_api_imports),
        ("数据目录创建", test_data_directory_creation),
        ("JSON文件操作", test_json_file_operations),
        ("环境变量检查", test_environment_variables),
        ("单个API调用", test_single_api_call),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 检查现有数据
    check_existing_data()
    
    # 测试结果总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！可以运行下载脚本")
        print("\n💡 运行下载脚本:")
        print("   python download_googl_tsla_financial_data.py")
    else:
        print("⚠️ 部分测试失败，请检查配置")
        print("\n💡 常见问题解决:")
        print("   1. 确保在项目根目录运行")
        print("   2. 检查.env文件中的API密钥")
        print("   3. 确保网络连接正常")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GOOGL和TSLA金融数据批量下载脚本

该脚本用于批量下载GOOGL和TSLA股票的金融数据到financial_data_offline目录，
支持多种数据类型的下载和缓存。

使用方法:
    python download_googl_tsla_financial_data.py
    python download_googl_tsla_financial_data.py --tickers GOOGL
    python download_googl_tsla_financial_data.py --start-date 2024-01-01 --end-date 2025-06-01
"""

import os
import sys
import json
import argparse
from datetime import datetime, timedelta
from pathlib import Path
import time
from typing import List, Dict, Any

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.tools.api import (
        get_line_items, get_market_cap, get_prices, 
        get_insider_trades, get_company_news, get_financial_metrics
    )
    print("✅ 成功导入API工具模块")
except ImportError as e:
    print(f"❌ 导入API工具模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

class FinancialDataDownloader:
    """金融数据下载器"""
    
    def __init__(self, base_dir: str = "financial_data_offline"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        
        # 支持的数据类型和对应的API函数
        self.data_types = {
            'line_items': get_line_items,
            'market_cap': get_market_cap,
            'prices': get_prices,
            'insider_trades': get_insider_trades,
            'company_news': get_company_news,
            'financial_metrics': get_financial_metrics
        }
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'cache_hits': 0
        }
        
        self.start_time = datetime.now()
    
    def generate_date_range(self, start_date: str, end_date: str, interval_days: int = 30) -> List[str]:
        """生成日期范围列表"""
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        
        dates = []
        current = start
        while current <= end:
            dates.append(current.strftime('%Y-%m-%d'))
            current += timedelta(days=interval_days)
        
        # 确保包含结束日期
        if dates[-1] != end_date:
            dates.append(end_date)
            
        return dates
    
    def create_data_directory(self, ticker: str, data_type: str) -> Path:
        """创建数据目录"""
        dir_path = self.base_dir / f"{ticker}_{data_type}"
        dir_path.mkdir(exist_ok=True)
        return dir_path
    
    def get_cache_filename(self, ticker: str, data_type: str, date: str) -> Path:
        """获取缓存文件名"""
        dir_path = self.create_data_directory(ticker, data_type)
        
        if data_type == 'prices':
            # 价格数据使用范围文件名
            return dir_path / f"{ticker}_prices_2024-01-01_to_2025-06-01.json"
        else:
            # 其他数据使用日期文件名
            return dir_path / f"{ticker}_{data_type}_{date}.json"
    
    def is_data_cached(self, ticker: str, data_type: str, date: str) -> bool:
        """检查数据是否已缓存"""
        cache_file = self.get_cache_filename(ticker, data_type, date)
        return cache_file.exists()
    
    def save_data_to_cache(self, ticker: str, data_type: str, date: str, data: Any, metadata: Dict = None):
        """保存数据到缓存"""
        cache_file = self.get_cache_filename(ticker, data_type, date)
        
        cache_data = {
            'data': data,
            'metadata': {
                'download_time': datetime.now().isoformat(),
                'api_source': 'FINANCIAL_DATASETS_API',
                'ticker': ticker,
                'end_date': date,
                'data_type': data_type,
                **(metadata or {})
            }
        }
        
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
            print(f"  💾 已保存: {cache_file.name}")
        except Exception as e:
            print(f"  ❌ 保存失败: {cache_file.name} - {e}")
    
    def download_data_type(self, ticker: str, data_type: str, dates: List[str]) -> Dict[str, int]:
        """下载特定类型的数据"""
        print(f"\n📊 下载 {ticker} 的 {data_type} 数据...")
        
        api_func = self.data_types[data_type]
        type_stats = {'success': 0, 'failed': 0, 'cached': 0}
        
        for date in dates:
            self.stats['total_requests'] += 1
            
            # 检查缓存
            if self.is_data_cached(ticker, data_type, date):
                print(f"  ✅ 缓存命中: {date}")
                self.stats['cache_hits'] += 1
                type_stats['cached'] += 1
                continue
            
            try:
                print(f"  🔄 下载: {date}")
                
                # 调用API获取数据
                if data_type == 'line_items':
                    # line_items需要特殊的line_items参数
                    line_items = [
                        "revenue", "net_income", "operating_income", "ebit",
                        "free_cash_flow", "total_debt", "interest_expense",
                        "capital_expenditure", "depreciation_and_amortization", "outstanding_shares"
                    ]
                    data = api_func(ticker, end_date=date, line_items=line_items)
                    metadata = {'line_items': line_items}
                elif data_type == 'prices':
                    # 价格数据使用日期范围
                    data = api_func(ticker, start_date="2024-01-01", end_date="2025-06-01")
                    metadata = {'start_date': "2024-01-01", 'end_date': "2025-06-01"}
                else:
                    # 其他数据类型
                    data = api_func(ticker, end_date=date)
                    metadata = {}
                
                if data:
                    self.save_data_to_cache(ticker, data_type, date, data, metadata)
                    self.stats['successful_requests'] += 1
                    type_stats['success'] += 1
                    print(f"  ✅ 成功: {date}")
                else:
                    print(f"  ⚠️ 无数据: {date}")
                    self.stats['failed_requests'] += 1
                    type_stats['failed'] += 1
                
                # 价格数据只需要下载一次
                if data_type == 'prices':
                    break
                    
                # API限制延迟
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ 失败: {date} - {e}")
                self.stats['failed_requests'] += 1
                type_stats['failed'] += 1
                time.sleep(1)  # 错误后稍长延迟
        
        return type_stats
    
    def download_ticker_data(self, ticker: str, start_date: str, end_date: str, data_types: List[str] = None):
        """下载单个股票的所有数据"""
        print(f"\n🎯 开始下载 {ticker} 的金融数据...")
        
        if data_types is None:
            data_types = list(self.data_types.keys())
        
        # 生成日期范围
        dates = self.generate_date_range(start_date, end_date)
        print(f"📅 日期范围: {start_date} 到 {end_date} ({len(dates)} 个时间点)")
        
        ticker_stats = {}
        
        for data_type in data_types:
            try:
                type_stats = self.download_data_type(ticker, data_type, dates)
                ticker_stats[data_type] = type_stats
                print(f"  📈 {data_type}: 成功 {type_stats['success']}, 缓存 {type_stats['cached']}, 失败 {type_stats['failed']}")
            except Exception as e:
                print(f"  ❌ {data_type} 下载失败: {e}")
                ticker_stats[data_type] = {'success': 0, 'cached': 0, 'failed': len(dates)}
        
        return ticker_stats
    
    def generate_report(self, ticker_stats: Dict[str, Dict[str, Dict[str, int]]]):
        """生成下载报告"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        report_lines = [
            "=" * 60,
            "GOOGL和TSLA金融数据下载报告",
            "=" * 60,
            f"总请求数: {self.stats['total_requests']}",
            f"成功请求: {self.stats['successful_requests']}",
            f"失败请求: {self.stats['failed_requests']}",
            f"缓存命中: {self.stats['cache_hits']}",
            f"成功率: {self.stats['successful_requests'] / max(1, self.stats['total_requests']) * 100:.1f}%",
            f"总耗时: {duration}",
            ""
        ]
        
        for ticker, data_types in ticker_stats.items():
            total_success = sum(stats['success'] for stats in data_types.values())
            total_requests = sum(stats['success'] + stats['failed'] for stats in data_types.values())
            
            report_lines.extend([
                f"股票: {ticker}",
                f"  成功: {total_success}/{total_requests}",
                ""
            ])
        
        # 生成目录结构
        report_lines.append("生成的目录结构:")
        for ticker in ticker_stats.keys():
            for data_type in self.data_types.keys():
                dir_path = self.base_dir / f"{ticker}_{data_type}"
                if dir_path.exists():
                    file_count = len(list(dir_path.glob("*.json")))
                    report_lines.append(f"  financial_data_offline/")
                    report_lines.append(f"    {ticker}_{data_type}/ ({file_count} 文件)")
        
        report_lines.append("=" * 60)
        
        # 保存报告
        report_file = self.base_dir / f"download_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        # 打印报告
        print('\n'.join(report_lines))
        print(f"\n📄 报告已保存到: {report_file}")

def main():
    parser = argparse.ArgumentParser(description='GOOGL和TSLA金融数据批量下载工具')
    parser.add_argument('--tickers', nargs='+', default=['GOOGL', 'TSLA'],
                       help='要下载的股票代码 (默认: GOOGL TSLA)')
    parser.add_argument('--start-date', default='2024-01-01',
                       help='开始日期 (默认: 2024-01-01)')
    parser.add_argument('--end-date', default='2025-06-01',
                       help='结束日期 (默认: 2025-06-01)')
    parser.add_argument('--data-types', nargs='+',
                       choices=['line_items', 'market_cap', 'prices', 'insider_trades', 'company_news', 'financial_metrics'],
                       help='要下载的数据类型 (默认: 全部)')
    parser.add_argument('--base-dir', default='financial_data_offline',
                       help='数据保存目录 (默认: financial_data_offline)')
    
    args = parser.parse_args()
    
    print("🚀 GOOGL和TSLA金融数据下载器")
    print(f"📊 股票代码: {', '.join(args.tickers)}")
    print(f"📅 时间范围: {args.start_date} 到 {args.end_date}")
    print(f"💾 保存目录: {args.base_dir}")
    
    downloader = FinancialDataDownloader(args.base_dir)
    ticker_stats = {}
    
    for ticker in args.tickers:
        try:
            stats = downloader.download_ticker_data(
                ticker, args.start_date, args.end_date, args.data_types
            )
            ticker_stats[ticker] = stats
        except Exception as e:
            print(f"❌ {ticker} 下载失败: {e}")
            ticker_stats[ticker] = {}
    
    downloader.generate_report(ticker_stats)
    print("\n✅ 下载完成！")

if __name__ == "__main__":
    main()
